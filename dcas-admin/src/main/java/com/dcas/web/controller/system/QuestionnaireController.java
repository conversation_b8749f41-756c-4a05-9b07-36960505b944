package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.annotation.RepeatSubmit;
import com.dcas.common.core.domain.R;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.model.dto.CommonDto;
import com.dcas.common.model.req.AbilityItemReq;
import com.dcas.common.model.req.QuestionnaireContentReq;
import com.dcas.common.model.vo.ItemAbilityVO;
import com.dcas.common.model.vo.OnlineQuestionContentVO;
import com.dcas.common.model.vo.QuestionnaireContentVO;
import com.dcas.system.service.QuestionnaireService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/2/14 15:33
 * @since 1.2.0
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/questionnaire")
@Api(tags = "问卷调研")
public class QuestionnaireController {

    private final QuestionnaireService questionnaireService;

    @PostMapping("/retrieve")
    @ApiOperation(tags = "问卷调研", value = "查询调研问卷")
    @Log(title = "查询调研问卷", businessType = BusinessType.QUERY, module = "评估作业")
    public R<QuestionnaireContentVO> selectQuestionnaire(@Validated @RequestBody CommonDto dto) {
        return R.success(questionnaireService.selectQuestionnaire(dto));
    }

    @PostMapping("/save")
    @ApiOperation(tags = "问卷调研", value = "保存调研问卷")
    @RepeatSubmit(interval = 1000, message = "请求过于频繁")
    @Log(title = "保存调研问卷", businessType = BusinessType.INSERT, module = "评估作业")
    public R<Boolean> saveAndUpdate(@Validated @RequestBody QuestionnaireContentReq req) {
        questionnaireService.saveAndUpdate(req);
        return R.success();
    }

    @GetMapping("/offline")
    @ApiOperation("离线问卷模板导出")
    @Log(title = "离线问卷模板导出", businessType = BusinessType.EXPORT, module = "评估作业")
    public void offlineQuestionnaireExport(@RequestParam String operationId, HttpServletResponse response) {
        questionnaireService.offlineQuestionnaireExport(operationId, response);
    }

    @PostMapping("/offline")
    @ApiOperation("离线问卷导入")
    @Log(title = "离线问卷导入", businessType = BusinessType.IMPORT, module = "评估作业")
    public R<Boolean> offlineQuestionnaireImport(@RequestParam String operationId, MultipartFile file) {
        questionnaireService.offlineQuestionnaireImport(operationId, file);
        return R.success();
    }

    @GetMapping("/online")
    @ApiOperation("在线问卷地址生成")
    @Log(title = "在线问卷地址生成", businessType = BusinessType.OTHER, module = "评估作业")
    public R<String> onlineQuestionnaireGenerate(@RequestParam String operationId, @RequestParam Integer jobType) {
        return R.success(questionnaireService.onlineQuestionnaireGenerate(operationId, jobType));
    }

    @PostMapping("/online/retrieve")
    @ApiOperation("查询在线问卷")
    public R<OnlineQuestionContentVO> selectOnlineQuestionnaire(@RequestBody CommonDto dto) {
        return R.success(questionnaireService.selectOnlineQuestionnaire(dto));
    }

    @PostMapping("/online/save")
    @ApiOperation("保存在线问卷")
    public R<Boolean> onlineQuestionnaireSave(@Validated @RequestBody OnlineQuestionContentVO vo) {
        questionnaireService.onlineQuestionnaireSave(vo);
        return R.success();
    }

    @GetMapping("/online/import")
    @ApiOperation("在线问卷结果导入")
    @Log(title = "在线问卷结果导入", businessType = BusinessType.IMPORT, module = "评估作业")
    public R<Boolean> onlineQuestionnaireImport(@RequestParam String operationId) {
        questionnaireService.onlineQuestionnaireImport(operationId);
        return R.success();
    }

    @PostMapping("/operation/ability/item")
    @ApiOperation(value = "查询能力项详情")
    public R<ItemAbilityVO> queryAbilityItem(@Validated @RequestBody AbilityItemReq req) {
        return R.success(questionnaireService.queryAbilityItem(req));
    }
}
